{"cells": [{"cell_type": "code", "execution_count": 1, "id": "18772359", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ok\n"]}], "source": ["print(\"ok\")"]}, {"cell_type": "code", "execution_count": 2, "id": "68b7c55e", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv"]}, {"cell_type": "code", "execution_count": 3, "id": "a0255cca", "metadata": {}, "outputs": [], "source": ["from langchain_groq import ChatGroq"]}, {"cell_type": "code", "execution_count": 4, "id": "a73c057d", "metadata": {}, "outputs": [], "source": ["llm=ChatGroq(model=\"deepseek-r1-distill-llama-70b\")"]}, {"cell_type": "code", "execution_count": 5, "id": "ec4c38f3", "metadata": {}, "outputs": [{"data": {"text/plain": ["AIMessage(content='<think>\\n\\n</think>\\n\\nHello! How can I assist you today? 😊', additional_kwargs={}, response_metadata={'token_usage': {'completion_tokens': 16, 'prompt_tokens': 4, 'total_tokens': 20, 'completion_time': 0.084725173, 'prompt_time': 6.0159e-05, 'queue_time': 0.053524741, 'total_time': 0.084785332}, 'model_name': 'deepseek-r1-distill-llama-70b', 'system_fingerprint': 'fp_1bbe7845ec', 'finish_reason': 'stop', 'logprobs': None}, id='run--8b091ea3-0a7d-4058-9e8b-2a68094cc078-0', usage_metadata={'input_tokens': 4, 'output_tokens': 16, 'total_tokens': 20})"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["llm.invoke(\"hi\")"]}, {"cell_type": "code", "execution_count": 6, "id": "0215d359", "metadata": {}, "outputs": [], "source": ["from langchain.tools import tool"]}, {"cell_type": "code", "execution_count": 7, "id": "d81ac345", "metadata": {}, "outputs": [], "source": ["@tool\n", "def multiply(a: int, b: int) -> int:\n", "    \"\"\"\n", "    Multiply two integers.\n", "\n", "    Args:\n", "        a (int): The first integer.\n", "        b (int): The second integer.\n", "\n", "    Returns:\n", "        int: The product of a and b.\n", "    \"\"\"\n", "    return a * b"]}, {"cell_type": "code", "execution_count": 8, "id": "1f79ae97", "metadata": {}, "outputs": [{"data": {"text/plain": ["StructuredTool(name='multiply', description='Multiply two integers.\\n\\nArgs:\\n    a (int): The first integer.\\n    b (int): The second integer.\\n\\nReturns:\\n    int: The product of a and b.', args_schema=<class 'langchain_core.utils.pydantic.multiply'>, func=<function multiply at 0x000002DFC40C3A30>)"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["multiply"]}, {"cell_type": "code", "execution_count": null, "id": "2927babf", "metadata": {}, "outputs": [], "source": ["from langchain_core.tools import StructuredTool"]}, {"cell_type": "code", "execution_count": null, "id": "03483480", "metadata": {}, "outputs": [], "source": ["class WeatherInput(BaseModel):\n", "    city: str"]}, {"cell_type": "code", "execution_count": null, "id": "28d0a2ca", "metadata": {}, "outputs": [], "source": ["def get_weather(city: str) -> str:\n", "    \"\"\"\n", "    Get the weather for a given city.\n", "\n", "    Args:\n", "        city (str): The name of the city.\n", "\n", "    Returns:\n", "        str: A string describing the weather in the city.\n", "    \"\"\"\n", "    return f\"The weather in {city} is sunny.\""]}, {"cell_type": "code", "execution_count": null, "id": "b45850a3", "metadata": {}, "outputs": [], "source": ["weather_tool = StructuredTool.from_function(\n", "    func=get_weather,\n", "    name=\"get_weather\",\n", "    description=\"Fetches real-time weather data for a city\",\n", "    args_schema=WeatherInput,  \n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "e36ee4d6", "metadata": {}, "outputs": [], "source": ["class WeatherInput(BaseModel):\n", "    city: str = Field(..., description=\"City name\")\n", "    units: str = Field(\"metric\", description=\"metric or imperial\")\n", "\n", "class GetWeatherTool(StructuredTool):\n", "    name: ClassVar[str] = \"get_weather\"           \n", "    description: ClassVar[str] = (\n", "        \"Fetches weather data for a city\"\n", "    )\n", "    args_schema: ClassVar[Type[BaseModel]] = WeatherInput\n", "\n", "    def _run(self, city: str, units: str = \"metric\") -> str:\n", "        return get_weather(city, units)"]}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.18"}}, "nbformat": 4, "nbformat_minor": 5}